import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/animated_background.dart';
import '../../core/widgets/cards/flat_card.dart';
import '../../core/widgets/cards/flat_feature_card.dart';
import '../../core/widgets/flat_button.dart';
import 'home_controller.dart';

class HomeContentView extends GetView<HomeController> {
  const HomeContentView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Nexed Mini',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: false,
        actions: [
          IconButton(
            onPressed: controller.logout,
            icon: Icon(
              Icons.logout_rounded,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
      body: AnimatedBackground(
        child: <PERSON><PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Welcome Section
                FlatCard(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Welcome back,',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Obx(() => Text(
                            controller.userName.value,
                            style: theme.textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          )),
                      const SizedBox(height: 16),
                      Text(
                        'You\'ve successfully logged in to our beautiful flat design app. Enjoy the modern interface and smooth navigation!',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Quick Actions
                Text(
                  'Quick Actions',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: FlatButton(
                        text: 'New Project',
                        onPressed: () =>
                            _showComingSoon(context, 'New Project'),
                        icon: Icons.add_circle_outline,
                        backgroundColor:
                            theme.colorScheme.primary.withValues(alpha: 0.1),
                        textColor: theme.colorScheme.primary,
                        outlined: true,
                        borderColor:
                            theme.colorScheme.primary.withValues(alpha: 0.3),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: FlatButton(
                        text: 'View Tasks',
                        onPressed: () =>
                            _showComingSoon(context, 'Task Management'),
                        icon: Icons.task_alt,
                        backgroundColor:
                            theme.colorScheme.secondary.withValues(alpha: 0.1),
                        textColor: theme.colorScheme.secondary,
                        outlined: true,
                        borderColor:
                            theme.colorScheme.secondary.withValues(alpha: 0.3),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Feature Cards
                Text(
                  'Features',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  children: [
                    FlatFeatureCard(
                      icon: Icons.palette_rounded,
                      title: 'Flat Design',
                      description: 'Clean and minimal interface',
                      iconColor: theme.colorScheme.primary,
                      onTap: () => _showComingSoon(context, 'Design System'),
                    ),
                    FlatFeatureCard(
                      icon: Icons.navigation_rounded,
                      title: 'Curved Navigation',
                      description: 'Beautiful bottom navigation',
                      iconColor: theme.colorScheme.secondary,
                      onTap: () => _showComingSoon(context, 'Navigation'),
                    ),
                    FlatFeatureCard(
                      icon: Icons.security_rounded,
                      title: 'Secure',
                      description: 'Advanced security measures',
                      iconColor: Colors.green,
                      onTap: () => _showComingSoon(context, 'Security'),
                    ),
                    FlatFeatureCard(
                      icon: Icons.speed_rounded,
                      title: 'Fast Performance',
                      description: 'Optimized for speed',
                      iconColor: Colors.orange,
                      onTap: () => _showComingSoon(context, 'Performance'),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Recent Activity
                Text(
                  'Recent Activity',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                FlatCard(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      _buildActivityItem(
                        context,
                        Icons.check_circle,
                        'Task completed',
                        '2 hours ago',
                        Colors.green,
                      ),
                      const SizedBox(height: 16),
                      _buildActivityItem(
                        context,
                        Icons.upload,
                        'File uploaded',
                        '4 hours ago',
                        Colors.blue,
                      ),
                      const SizedBox(height: 16),
                      _buildActivityItem(
                        context,
                        Icons.person_add,
                        'New team member',
                        '1 day ago',
                        Colors.purple,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    BuildContext context,
    IconData icon,
    String title,
    String time,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              Text(
                time,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    Get.snackbar(
      'Coming Soon',
      '$feature feature will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }
}
