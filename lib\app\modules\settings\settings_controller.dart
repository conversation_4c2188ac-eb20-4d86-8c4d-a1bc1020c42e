import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/services/storage_service.dart';

class SettingsController extends GetxController {
  final RxBool isDarkMode = false.obs;
  final RxBool notificationsEnabled = true.obs;
  final RxBool biometricEnabled = false.obs;
  final RxBool autoBackup = true.obs;
  final RxString selectedLanguage = 'English'.obs;
  final RxDouble fontSize = 1.0.obs;

  final List<String> availableLanguages = [
    'English',
    'Spanish',
    'French',
    'German',
    'Chinese',
    'Japanese',
  ];

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
  }

  void _loadSettings() {
    // Load settings from storage
    final storage = StorageService.to;
    isDarkMode.value = storage.isDarkMode;
    notificationsEnabled.value = storage.getBool('notifications_enabled');
    biometricEnabled.value = storage.getBool('biometric_enabled');
    autoBackup.value = storage.getBool('auto_backup');
    selectedLanguage.value = storage.getString('selected_language').isEmpty
        ? 'English'
        : storage.getString('selected_language');
    fontSize.value = storage.getDouble('font_size') == 0.0
        ? 1.0
        : storage.getDouble('font_size');
  }

  void toggleDarkMode() {
    isDarkMode.value = !isDarkMode.value;
    Get.changeThemeMode(isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
    StorageService.to.setBool('dark_mode', isDarkMode.value);
  }

  void toggleNotifications() {
    notificationsEnabled.value = !notificationsEnabled.value;
    StorageService.to
        .setBool('notifications_enabled', notificationsEnabled.value);

    // Show snackbar
    Get.snackbar(
      'Notifications',
      notificationsEnabled.value
          ? 'Notifications enabled'
          : 'Notifications disabled',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void toggleBiometric() {
    biometricEnabled.value = !biometricEnabled.value;
    StorageService.to.setBool('biometric_enabled', biometricEnabled.value);

    Get.snackbar(
      'Biometric Authentication',
      biometricEnabled.value
          ? 'Biometric authentication enabled'
          : 'Biometric authentication disabled',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void toggleAutoBackup() {
    autoBackup.value = !autoBackup.value;
    StorageService.to.setBool('auto_backup', autoBackup.value);

    Get.snackbar(
      'Auto Backup',
      autoBackup.value ? 'Auto backup enabled' : 'Auto backup disabled',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void changeLanguage(String language) {
    selectedLanguage.value = language;
    StorageService.to.setString('selected_language', language);

    Get.snackbar(
      'Language',
      'Language changed to $language',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void changeFontSize(double size) {
    fontSize.value = size;
    StorageService.to.setDouble('font_size', size);
  }

  void showAbout() {
    Get.dialog(
      AlertDialog(
        title: const Text('About'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Nexed Mini'),
            SizedBox(height: 8),
            Text('Version: 1.0.0'),
            SizedBox(height: 8),
            Text('A beautiful Flutter app with flat design'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void clearCache() {
    Get.dialog(
      AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('Are you sure you want to clear the app cache?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.snackbar(
                'Cache Cleared',
                'App cache has been cleared successfully',
                snackPosition: SnackPosition.BOTTOM,
                duration: const Duration(seconds: 2),
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void resetSettings() {
    Get.dialog(
      AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
            'Are you sure you want to reset all settings to default?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _resetToDefaults();
              Get.snackbar(
                'Settings Reset',
                'All settings have been reset to default',
                snackPosition: SnackPosition.BOTTOM,
                duration: const Duration(seconds: 2),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _resetToDefaults() {
    isDarkMode.value = false;
    notificationsEnabled.value = true;
    biometricEnabled.value = false;
    autoBackup.value = true;
    selectedLanguage.value = 'English';
    fontSize.value = 1.0;

    // Save to storage
    StorageService.to.setBool('dark_mode', false);
    StorageService.to.setBool('notifications_enabled', true);
    StorageService.to.setBool('biometric_enabled', false);
    StorageService.to.setBool('auto_backup', true);
    StorageService.to.setString('selected_language', 'English');
    StorageService.to.setDouble('font_size', 1.0);

    Get.changeThemeMode(ThemeMode.light);
  }
}
