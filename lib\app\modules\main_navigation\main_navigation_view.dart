import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/curved_bottom_navigation.dart';
import '../home/<USER>';
import '../settings/settings_view.dart';
import '../profile/profile_view.dart';
import 'main_navigation_controller.dart';

class MainNavigationView extends GetView<MainNavigationController> {
  const MainNavigationView({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Widget> pages = [
      const HomeContentView(),
      const SettingsView(),
      const ProfileView(),
    ];

    final List<CurvedBottomNavigationItem> navItems = [
      const CurvedBottomNavigationItem(
        icon: Icons.home_rounded,
        label: 'Home',
      ),
      const CurvedBottomNavigationItem(
        icon: Icons.settings_rounded,
        label: 'Settings',
      ),
      const CurvedBottomNavigationItem(
        icon: Icons.person_rounded,
        label: 'Profile',
      ),
    ];

    return Scaffold(
      body: Obx(() => IndexedStack(
            index: controller.currentIndex.value,
            children: pages,
          )),
      bottomNavigationBar: Obx(() => CurvedBottomNavigation(
            currentIndex: controller.currentIndex.value,
            onTap: controller.changeTab,
            items: navItems,
          )),
    );
  }
}
