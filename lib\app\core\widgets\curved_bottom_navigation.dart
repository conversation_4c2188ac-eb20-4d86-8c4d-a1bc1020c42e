import 'package:flutter/material.dart';

class CurvedBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<CurvedBottomNavigationItem> items;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double height;

  const CurvedBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.height = 80,
  });

  @override
  State<CurvedBottomNavigation> createState() => _CurvedBottomNavigationState();
}

class _CurvedBottomNavigationState extends State<CurvedBottomNavigation>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void didUpdateWidget(CurvedBottomNavigation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _animationController.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final backgroundColor = widget.backgroundColor ?? theme.colorScheme.surface;
    final selectedColor = widget.selectedItemColor ?? theme.colorScheme.primary;
    final unselectedColor = widget.unselectedItemColor ??
        theme.colorScheme.onSurface.withOpacity(0.6);

    return SizedBox(
      height: widget.height + MediaQuery.of(context).padding.bottom,
      child: Stack(
        children: [
          // Background with curve
          CustomPaint(
            size: Size(MediaQuery.of(context).size.width, widget.height),
            painter: CurvedBottomNavigationPainter(
              backgroundColor: backgroundColor,
              selectedIndex: widget.currentIndex,
              itemCount: widget.items.length,
              selectedColor: selectedColor,
            ),
          ),
          // Navigation items
          Positioned(
            bottom: MediaQuery.of(context).padding.bottom,
            left: 0,
            right: 0,
            height: widget.height,
            child: Row(
              children: widget.items.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                final isSelected = index == widget.currentIndex;

                return Expanded(
                  child: GestureDetector(
                    onTap: () => widget.onTap(index),
                    child: AnimatedBuilder(
                      animation: _animation,
                      builder: (context, child) {
                        return SizedBox(
                          height: widget.height,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                                transform: Matrix4.identity()
                                  ..translate(0.0, isSelected ? -8.0 : 0.0),
                                child: Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? selectedColor.withOpacity(0.1)
                                        : Colors.transparent,
                                    borderRadius: BorderRadius.circular(24),
                                  ),
                                  child: Icon(
                                    item.icon,
                                    color: isSelected
                                        ? selectedColor
                                        : unselectedColor,
                                    size: 24,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 4),
                              AnimatedDefaultTextStyle(
                                duration: const Duration(milliseconds: 300),
                                style: theme.textTheme.labelSmall!.copyWith(
                                  color: isSelected
                                      ? selectedColor
                                      : unselectedColor,
                                  fontWeight: isSelected
                                      ? FontWeight.w600
                                      : FontWeight.w400,
                                ),
                                child: Text(item.label),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

class CurvedBottomNavigationItem {
  final IconData icon;
  final String label;

  const CurvedBottomNavigationItem({
    required this.icon,
    required this.label,
  });
}

class CurvedBottomNavigationPainter extends CustomPainter {
  final Color backgroundColor;
  final int selectedIndex;
  final int itemCount;
  final Color selectedColor;

  CurvedBottomNavigationPainter({
    required this.backgroundColor,
    required this.selectedIndex,
    required this.itemCount,
    required this.selectedColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.1)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    final path = Path();
    final shadowPath = Path();

    // Calculate item width
    final itemWidth = size.width / itemCount;
    final selectedItemCenter = (selectedIndex + 0.5) * itemWidth;

    // Create curved path
    path.moveTo(0, 20);
    shadowPath.moveTo(0, 20);

    // Left side curve
    if (selectedItemCenter > itemWidth * 0.5) {
      path.lineTo(selectedItemCenter - 40, 20);
      shadowPath.lineTo(selectedItemCenter - 40, 20);
    }

    // Create the curve around selected item
    path.quadraticBezierTo(
      selectedItemCenter - 20,
      20,
      selectedItemCenter - 20,
      0,
    );
    path.quadraticBezierTo(
      selectedItemCenter,
      -15,
      selectedItemCenter + 20,
      0,
    );
    path.quadraticBezierTo(
      selectedItemCenter + 20,
      20,
      selectedItemCenter + 40,
      20,
    );

    shadowPath.quadraticBezierTo(
      selectedItemCenter - 20,
      20,
      selectedItemCenter - 20,
      0,
    );
    shadowPath.quadraticBezierTo(
      selectedItemCenter,
      -15,
      selectedItemCenter + 20,
      0,
    );
    shadowPath.quadraticBezierTo(
      selectedItemCenter + 20,
      20,
      selectedItemCenter + 40,
      20,
    );

    // Right side
    if (selectedItemCenter < size.width - itemWidth * 0.5) {
      path.lineTo(size.width, 20);
      shadowPath.lineTo(size.width, 20);
    }

    // Complete the path
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    shadowPath.lineTo(size.width, 20);
    shadowPath.lineTo(size.width, size.height);
    shadowPath.lineTo(0, size.height);
    shadowPath.close();

    // Draw shadow
    canvas.drawPath(shadowPath, shadowPaint);

    // Draw main background
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CurvedBottomNavigationPainter oldDelegate) {
    return oldDelegate.selectedIndex != selectedIndex ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.selectedColor != selectedColor;
  }
}
